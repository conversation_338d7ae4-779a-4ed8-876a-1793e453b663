<?xml version="1.0" encoding="UTF-8"?>
<mxfile host="app.diagrams.net" agent="5.0" version="24.7.17">
  <diagram name="RSSI差分特征构建" id="rssi-differential-feature">
    <mxGraphModel dx="1422" dy="754" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="1" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        
        <!-- Input RSSI Matrix -->
        <mxCell id="input_matrix" value="$$\mathbf{R} = \begin{bmatrix} r_{11} &amp; r_{12} &amp; r_{13} &amp; r_{14} \\ r_{21} &amp; r_{22} &amp; r_{23} &amp; r_{24} \\ r_{31} &amp; r_{32} &amp; r_{33} &amp; r_{34} \\ r_{41} &amp; r_{42} &amp; r_{43} &amp; r_{44} \end{bmatrix}$$" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=14;fontFamily=Times New Roman;align=center;" vertex="1" parent="1">
          <mxGeometry x="50" y="80" width="180" height="100" as="geometry" />
        </mxCell>
        
        <!-- MinMax Normalization -->
        <mxCell id="norm_step" value="$$\text{RSSI}_{\text{norm}} = \frac{\text{RSSI} - \min}{\max - \min}$$" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=12;fontFamily=Times New Roman;align=center;" vertex="1" parent="1">
          <mxGeometry x="320" y="80" width="200" height="100" as="geometry" />
        </mxCell>
        
        <!-- Arrow 1 -->
        <mxCell id="arrow1" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=3;strokeColor=#2E86AB;endArrow=classic;endSize=8;" edge="1" parent="1" source="input_matrix" target="norm_step">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- Normalized Matrix -->
        <mxCell id="norm_matrix" value="$$\mathbf{R}_{\text{norm}} = \begin{bmatrix} 0.2 &amp; 0.8 &amp; 0.1 &amp; 0.9 \\ 0.7 &amp; 0.3 &amp; 0.6 &amp; 0.4 \\ 0.5 &amp; 0.1 &amp; 0.8 &amp; 0.2 \\ 0.9 &amp; 0.6 &amp; 0.3 &amp; 0.7 \end{bmatrix}$$" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=12;fontFamily=Times New Roman;align=center;" vertex="1" parent="1">
          <mxGeometry x="580" y="80" width="180" height="100" as="geometry" />
        </mxCell>
        
        <!-- Arrow 2 -->
        <mxCell id="arrow2" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=3;strokeColor=#2E86AB;endArrow=classic;endSize=8;" edge="1" parent="1" source="norm_step" target="norm_matrix">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- Differential Calculation -->
        <mxCell id="diff_calc" value="$$\Delta\text{RSSI}_{i,j} = r_i - r_j$$&lt;br&gt;$$\forall i&lt;j$$" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=14;fontFamily=Times New Roman;align=center;" vertex="1" parent="1">
          <mxGeometry x="320" y="250" width="200" height="80" as="geometry" />
        </mxCell>
        
        <!-- Arrow down -->
        <mxCell id="arrow_down" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=3;strokeColor=#2E86AB;endArrow=classic;endSize=8;" edge="1" parent="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="420" y="180" as="sourcePoint" />
            <mxPoint x="420" y="250" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- Differential pairs visualization -->
        <mxCell id="pair_viz" value="$$\begin{bmatrix} (1,2) \\ (1,3) \\ (1,4) \\ (2,3) \\ (2,4) \\ (3,4) \end{bmatrix}$$&lt;br&gt;$$C_4^2 = 6$$" style="ellipse;whiteSpace=wrap;html=1;fillColor=#e6f3ff;strokeColor=#4a90e2;fontSize=12;fontFamily=Times New Roman;align=center;" vertex="1" parent="1">
          <mxGeometry x="50" y="240" width="120" height="100" as="geometry" />
        </mxCell>
        
        <!-- Differential Feature Vector -->
        <mxCell id="diff_vector" value="$$\mathbf{\Delta F} = \begin{bmatrix} \Delta_{12} \\ \Delta_{13} \\ \Delta_{14} \\ \Delta_{23} \\ \Delta_{24} \\ \Delta_{34} \end{bmatrix}$$" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#e6f3ff;strokeColor=#4a90e2;fontSize=14;fontFamily=Times New Roman;align=center;" vertex="1" parent="1">
          <mxGeometry x="580" y="240" width="120" height="100" as="geometry" />
        </mxCell>
        
        <!-- Arrow 3 -->
        <mxCell id="arrow3" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=3;strokeColor=#2E86AB;endArrow=classic;endSize=8;" edge="1" parent="1" source="diff_calc" target="diff_vector">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- Nonlinear Transformation -->
        <mxCell id="nonlinear" value="$$f(\mathbf{x}) = \sigma(\mathbf{W}\mathbf{x} + \mathbf{b})$$&lt;br&gt;$$\sigma = \text{ReLU}$$" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=14;fontFamily=Times New Roman;align=center;" vertex="1" parent="1">
          <mxGeometry x="320" y="410" width="200" height="80" as="geometry" />
        </mxCell>
        
        <!-- Arrow down 2 -->
        <mxCell id="arrow_down2" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=3;strokeColor=#2E86AB;endArrow=classic;endSize=8;" edge="1" parent="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="420" y="340" as="sourcePoint" />
            <mxPoint x="420" y="410" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- Concatenated Features -->
        <mxCell id="concat_features" value="$$\mathbf{x} = [\mathbf{x}_{\text{raw}}; \mathbf{x}_{\text{diff}}]$$" style="ellipse;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=12;fontFamily=Times New Roman;align=center;" vertex="1" parent="1">
          <mxGeometry x="50" y="420" width="140" height="60" as="geometry" />
        </mxCell>
        
        <!-- Final Enhanced Features -->
        <mxCell id="enhanced_features" value="$$\mathbf{F}_{\text{enhanced}}$$&lt;br&gt;Enhanced Features" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#ffe6e6;strokeColor=#cc0000;fontSize=16;fontFamily=Times New Roman;fontStyle=1;align=center;" vertex="1" parent="1">
          <mxGeometry x="580" y="410" width="140" height="80" as="geometry" />
        </mxCell>
        
        <!-- Arrow 4 -->
        <mxCell id="arrow4" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=3;strokeColor=#2E86AB;endArrow=classic;endSize=8;" edge="1" parent="1" source="nonlinear" target="enhanced_features">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- Dimension labels -->
        <mxCell id="dim_label1" value="16-D" style="text;strokeColor=none;fillColor=none;html=1;fontSize=12;fontStyle=1;verticalAlign=middle;align=center;fontFamily=Times New Roman;color=#666666;" vertex="1" parent="1">
          <mxGeometry x="130" y="50" width="40" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="dim_label2" value="6-D" style="text;strokeColor=none;fillColor=none;html=1;fontSize=12;fontStyle=1;verticalAlign=middle;align=center;fontFamily=Times New Roman;color=#666666;" vertex="1" parent="1">
          <mxGeometry x="630" y="210" width="30" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="dim_label3" value="Enhanced-D" style="text;strokeColor=none;fillColor=none;html=1;fontSize=12;fontStyle=1;verticalAlign=middle;align=center;fontFamily=Times New Roman;color=#666666;" vertex="1" parent="1">
          <mxGeometry x="600" y="500" width="80" height="20" as="geometry" />
        </mxCell>
        
        <!-- Process indicators -->
        <mxCell id="process1" value="Normalization" style="text;strokeColor=none;fillColor=none;html=1;fontSize=10;fontStyle=2;verticalAlign=middle;align=center;fontFamily=Times New Roman;color=#9673a6;" vertex="1" parent="1">
          <mxGeometry x="270" y="110" width="80" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="process2" value="Differential" style="text;strokeColor=none;fillColor=none;html=1;fontSize=10;fontStyle=2;verticalAlign=middle;align=center;fontFamily=Times New Roman;color=#b85450;" vertex="1" parent="1">
          <mxGeometry x="270" y="270" width="60" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="process3" value="Nonlinear" style="text;strokeColor=none;fillColor=none;html=1;fontSize=10;fontStyle=2;verticalAlign=middle;align=center;fontFamily=Times New Roman;color=#82b366;" vertex="1" parent="1">
          <mxGeometry x="270" y="430" width="60" height="20" as="geometry" />
        </mxCell>
        
        <!-- Data flow indicators -->
        <mxCell id="data_flow" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=6;strokeColor=#ff6b35;dashed=1;dashPattern=8 8;" edge="1" parent="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="20" y="130" as="sourcePoint" />
            <mxPoint x="20" y="450" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>