<mxfile host="app.diagrams.net" modified="2024-01-01T00:00:00.000Z" agent="5.0" etag="xxx" version="22.1.16" type="device">
  <diagram name="异构图注意力网络技术架构" id="heterogeneous-graph-tech">
    <mxGraphModel dx="1600" dy="900" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1200" pageHeight="800" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />

        <!-- 主标题 -->
        <mxCell id="main-title" value="异构图注意力网络架构与信号传播机制" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=18;fontStyle=1;fontColor=#2F5233;" vertex="1" parent="1">
          <mxGeometry x="350" y="20" width="500" height="30" as="geometry" />
        </mxCell>

        <!-- 左侧：RFID信号传播层 -->
        <mxCell id="signal-layer-bg" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#F0F8FF;strokeColor=#4A90E2;strokeWidth=2;opacity=30;" vertex="1" parent="1">
          <mxGeometry x="50" y="80" width="300" height="200" as="geometry" />
        </mxCell>

        <mxCell id="signal-layer-title" value="信号传播层" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;fontStyle=1;fontColor=#4A90E2;" vertex="1" parent="1">
          <mxGeometry x="170" y="90" width="80" height="20" as="geometry" />
        </mxCell>

        <!-- 天线阵列 -->
        <mxCell id="antenna-array" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#D5E8D4;strokeColor=#82B366;strokeWidth=3;" vertex="1" parent="1">
          <mxGeometry x="80" y="120" width="240" height="40" as="geometry" />
        </mxCell>

        <!-- 天线单元 -->
        <mxCell id="ant1" value="" style="triangle;whiteSpace=wrap;html=1;fillColor=#82B366;strokeColor=#82B366;direction=south;" vertex="1" parent="1">
          <mxGeometry x="100" y="125" width="15" height="30" as="geometry" />
        </mxCell>

        <mxCell id="ant2" value="" style="triangle;whiteSpace=wrap;html=1;fillColor=#82B366;strokeColor=#82B366;direction=south;" vertex="1" parent="1">
          <mxGeometry x="140" y="125" width="15" height="30" as="geometry" />
        </mxCell>

        <mxCell id="ant3" value="" style="triangle;whiteSpace=wrap;html=1;fillColor=#82B366;strokeColor=#82B366;direction=south;" vertex="1" parent="1">
          <mxGeometry x="180" y="125" width="15" height="30" as="geometry" />
        </mxCell>

        <mxCell id="ant4" value="" style="triangle;whiteSpace=wrap;html=1;fillColor=#82B366;strokeColor=#82B366;direction=south;" vertex="1" parent="1">
          <mxGeometry x="220" y="125" width="15" height="30" as="geometry" />
        </mxCell>

        <mxCell id="ant5" value="" style="triangle;whiteSpace=wrap;html=1;fillColor=#82B366;strokeColor=#82B366;direction=south;" vertex="1" parent="1">
          <mxGeometry x="260" y="125" width="15" height="30" as="geometry" />
        </mxCell>

        <mxCell id="ant6" value="" style="triangle;whiteSpace=wrap;html=1;fillColor=#82B366;strokeColor=#82B366;direction=south;" vertex="1" parent="1">
          <mxGeometry x="300" y="125" width="15" height="30" as="geometry" />
        </mxCell>

        <!-- RFID标签 -->
        <mxCell id="tag-cluster" value="" style="ellipse;whiteSpace=wrap;html=1;fillColor=#E1D5E7;strokeColor=#9673A6;strokeWidth=2;opacity=80;" vertex="1" parent="1">
          <mxGeometry x="120" y="200" width="160" height="60" as="geometry" />
        </mxCell>

        <!-- 标签个体 -->
        <mxCell id="tag1" value="" style="ellipse;whiteSpace=wrap;html=1;fillColor=#9673A6;strokeColor=#9673A6;" vertex="1" parent="1">
          <mxGeometry x="140" y="215" width="12" height="12" as="geometry" />
        </mxCell>

        <mxCell id="tag2" value="" style="ellipse;whiteSpace=wrap;html=1;fillColor=#9673A6;strokeColor=#9673A6;" vertex="1" parent="1">
          <mxGeometry x="170" y="210" width="12" height="12" as="geometry" />
        </mxCell>

        <mxCell id="tag3" value="" style="ellipse;whiteSpace=wrap;html=1;fillColor=#9673A6;strokeColor=#9673A6;" vertex="1" parent="1">
          <mxGeometry x="200" y="220" width="12" height="12" as="geometry" />
        </mxCell>

        <mxCell id="tag4" value="" style="ellipse;whiteSpace=wrap;html=1;fillColor=#9673A6;strokeColor=#9673A6;" vertex="1" parent="1">
          <mxGeometry x="230" y="215" width="12" height="12" as="geometry" />
        </mxCell>

        <mxCell id="tag5" value="" style="ellipse;whiteSpace=wrap;html=1;fillColor=#9673A6;strokeColor=#9673A6;" vertex="1" parent="1">
          <mxGeometry x="260" y="235" width="12" height="12" as="geometry" />
        </mxCell>

        <!-- RSSI信号强度可视化 -->
        <mxCell id="rssi-wave1" value="" style="curved=1;endArrow=none;html=1;strokeColor=#FF6B6B;strokeWidth=4;opacity=70;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="107" y="155" as="sourcePoint" />
            <mxPoint x="146" y="215" as="targetPoint" />
            <Array as="points">
              <mxPoint x="120" y="175" />
              <mxPoint x="130" y="190" />
            </Array>
          </mxGeometry>
        </mxCell>

        <mxCell id="rssi-wave2" value="" style="curved=1;endArrow=none;html=1;strokeColor=#FF8E53;strokeWidth=3;opacity=60;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="147" y="155" as="sourcePoint" />
            <mxPoint x="176" y="210" as="targetPoint" />
            <Array as="points">
              <mxPoint x="155" y="175" />
              <mxPoint x="165" y="190" />
            </Array>
          </mxGeometry>
        </mxCell>

        <mxCell id="rssi-wave3" value="" style="curved=1;endArrow=none;html=1;strokeColor=#FFD93D;strokeWidth=2;opacity=50;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="187" y="155" as="sourcePoint" />
            <mxPoint x="206" y="220" as="targetPoint" />
            <Array as="points">
              <mxPoint x="195" y="175" />
              <mxPoint x="200" y="195" />
            </Array>
          </mxGeometry>
        </mxCell>

        <!-- 中间：异构图抽象层 -->
        <mxCell id="graph-layer-bg" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFF8DC;strokeColor=#D4AC0D;strokeWidth=2;opacity=30;" vertex="1" parent="1">
          <mxGeometry x="400" y="80" width="350" height="200" as="geometry" />
        </mxCell>

        <mxCell id="graph-layer-title" value="异构图抽象层" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;fontStyle=1;fontColor=#D4AC0D;" vertex="1" parent="1">
          <mxGeometry x="540" y="90" width="100" height="20" as="geometry" />
        </mxCell>

        <!-- 标签节点层 -->
        <mxCell id="tag-layer" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E8E3F0;strokeColor=#9673A6;strokeWidth=1;opacity=50;" vertex="1" parent="1">
          <mxGeometry x="420" y="120" width="310" height="50" as="geometry" />
        </mxCell>

        <mxCell id="tag-layer-label" value="标签节点层" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontColor=#9673A6;" vertex="1" parent="1">
          <mxGeometry x="430" y="125" width="60" height="15" as="geometry" />
        </mxCell>

        <!-- 标签节点 - 不同大小表示重要性 -->
        <mxCell id="tag-node1" value="" style="ellipse;whiteSpace=wrap;html=1;fillColor=#9673A6;strokeColor=#9673A6;opacity=90;" vertex="1" parent="1">
          <mxGeometry x="500" y="135" width="20" height="20" as="geometry" />
        </mxCell>

        <mxCell id="tag-node2" value="" style="ellipse;whiteSpace=wrap;html=1;fillColor=#9673A6;strokeColor=#9673A6;opacity=70;" vertex="1" parent="1">
          <mxGeometry x="540" y="130" width="25" height="25" as="geometry" />
        </mxCell>

        <mxCell id="tag-node3" value="" style="ellipse;whiteSpace=wrap;html=1;fillColor=#9673A6;strokeColor=#9673A6;opacity=80;" vertex="1" parent="1">
          <mxGeometry x="580" y="135" width="22" height="22" as="geometry" />
        </mxCell>

        <mxCell id="tag-node4" value="" style="ellipse;whiteSpace=wrap;html=1;fillColor=#9673A6;strokeColor=#9673A6;opacity=60;" vertex="1" parent="1">
          <mxGeometry x="620" y="140" width="18" height="18" as="geometry" />
        </mxCell>

        <mxCell id="tag-node5" value="" style="ellipse;whiteSpace=wrap;html=1;fillColor=#9673A6;strokeColor=#9673A6;opacity=85;" vertex="1" parent="1">
          <mxGeometry x="660" y="135" width="24" height="24" as="geometry" />
        </mxCell>

        <!-- 天线节点层 -->
        <mxCell id="antenna-layer" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E8F5E8;strokeColor=#82B366;strokeWidth=1;opacity=50;" vertex="1" parent="1">
          <mxGeometry x="420" y="210" width="310" height="50" as="geometry" />
        </mxCell>

        <mxCell id="antenna-layer-label" value="天线节点层" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontColor=#82B366;" vertex="1" parent="1">
          <mxGeometry x="430" y="215" width="60" height="15" as="geometry" />
        </mxCell>

        <!-- 天线节点 -->
        <mxCell id="antenna-node1" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#82B366;strokeColor=#82B366;" vertex="1" parent="1">
          <mxGeometry x="520" y="230" width="20" height="20" as="geometry" />
        </mxCell>

        <mxCell id="antenna-node2" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#82B366;strokeColor=#82B366;" vertex="1" parent="1">
          <mxGeometry x="570" y="230" width="20" height="20" as="geometry" />
        </mxCell>

        <mxCell id="antenna-node3" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#82B366;strokeColor=#82B366;" vertex="1" parent="1">
          <mxGeometry x="620" y="230" width="20" height="20" as="geometry" />
        </mxCell>

        <mxCell id="antenna-node4" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#82B366;strokeColor=#82B366;" vertex="1" parent="1">
          <mxGeometry x="670" y="230" width="20" height="20" as="geometry" />
        </mxCell>

        <!-- 注意力权重连接 - 用不同粗细表示权重大小 -->
        <mxCell id="attention-edge1" value="" style="endArrow=none;html=1;strokeColor=#FF6B6B;strokeWidth=6;opacity=80;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="510" y="155" as="sourcePoint" />
            <mxPoint x="530" y="230" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="attention-edge2" value="" style="endArrow=none;html=1;strokeColor=#FF8E53;strokeWidth=4;opacity=70;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="552" y="155" as="sourcePoint" />
            <mxPoint x="580" y="230" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="attention-edge3" value="" style="endArrow=none;html=1;strokeColor=#FFD93D;strokeWidth=2;opacity=60;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="591" y="157" as="sourcePoint" />
            <mxPoint x="630" y="230" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- 标签间连接 - K近邻 -->
        <mxCell id="knn-edge1" value="" style="endArrow=none;html=1;strokeColor=#9673A6;strokeWidth=3;dashed=1;opacity=70;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="520" y="145" as="sourcePoint" />
            <mxPoint x="540" y="142" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="knn-edge2" value="" style="endArrow=none;html=1;strokeColor=#9673A6;strokeWidth=2;dashed=1;opacity=60;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="565" y="142" as="sourcePoint" />
            <mxPoint x="580" y="147" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- 右侧：注意力机制层 -->
        <mxCell id="attention-layer-bg" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#F0F0F8;strokeColor=#6C5CE7;strokeWidth=2;opacity=30;" vertex="1" parent="1">
          <mxGeometry x="800" y="80" width="350" height="200" as="geometry" />
        </mxCell>

        <mxCell id="attention-layer-title" value="多头注意力机制层" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;fontStyle=1;fontColor=#6C5CE7;" vertex="1" parent="1">
          <mxGeometry x="920" y="90" width="120" height="20" as="geometry" />
        </mxCell>

        <!-- 多头注意力可视化 -->
        <mxCell id="head1-bg" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFE5E5;strokeColor=#FF6B6B;strokeWidth=1;" vertex="1" parent="1">
          <mxGeometry x="820" y="120" width="80" height="40" as="geometry" />
        </mxCell>

        <mxCell id="head1-label" value="Head 1" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontColor=#FF6B6B;" vertex="1" parent="1">
          <mxGeometry x="840" y="130" width="40" height="20" as="geometry" />
        </mxCell>

        <mxCell id="head2-bg" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFE5CC;strokeColor=#FF8E53;strokeWidth=1;" vertex="1" parent="1">
          <mxGeometry x="920" y="120" width="80" height="40" as="geometry" />
        </mxCell>

        <mxCell id="head2-label" value="Head 2" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontColor=#FF8E53;" vertex="1" parent="1">
          <mxGeometry x="940" y="130" width="40" height="20" as="geometry" />
        </mxCell>

        <mxCell id="head3-bg" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFF5CC;strokeColor=#FFD93D;strokeWidth=1;" vertex="1" parent="1">
          <mxGeometry x="1020" y="120" width="80" height="40" as="geometry" />
        </mxCell>

        <mxCell id="head3-label" value="Head 3" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontColor=#FFD93D;" vertex="1" parent="1">
          <mxGeometry x="1040" y="130" width="40" height="20" as="geometry" />
        </mxCell>

        <!-- 注意力权重矩阵可视化 -->
        <mxCell id="attention-matrix" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#E8E8F0;strokeColor=#6C5CE7;strokeWidth=1;" vertex="1" parent="1">
          <mxGeometry x="850" y="180" width="200" height="80" as="geometry" />
        </mxCell>

        <!-- 权重热力图 -->
        <mxCell id="weight-cell1" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#FF6B6B;strokeColor=none;opacity=90;" vertex="1" parent="1">
          <mxGeometry x="860" y="190" width="15" height="15" as="geometry" />
        </mxCell>

        <mxCell id="weight-cell2" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#FF8E53;strokeColor=none;opacity=70;" vertex="1" parent="1">
          <mxGeometry x="880" y="190" width="15" height="15" as="geometry" />
        </mxCell>

        <mxCell id="weight-cell3" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#FFD93D;strokeColor=none;opacity=50;" vertex="1" parent="1">
          <mxGeometry x="900" y="190" width="15" height="15" as="geometry" />
        </mxCell>

        <mxCell id="weight-cell4" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#A8E6CF;strokeColor=none;opacity=40;" vertex="1" parent="1">
          <mxGeometry x="920" y="190" width="15" height="15" as="geometry" />
        </mxCell>

        <mxCell id="weight-cell5" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#B8B8FF;strokeColor=none;opacity=30;" vertex="1" parent="1">
          <mxGeometry x="940" y="190" width="15" height="15" as="geometry" />
        </mxCell>

        <!-- 第二行权重 -->
        <mxCell id="weight-cell6" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#FF8E53;strokeColor=none;opacity=80;" vertex="1" parent="1">
          <mxGeometry x="860" y="210" width="15" height="15" as="geometry" />
        </mxCell>

        <mxCell id="weight-cell7" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#FFD93D;strokeColor=none;opacity=60;" vertex="1" parent="1">
          <mxGeometry x="880" y="210" width="15" height="15" as="geometry" />
        </mxCell>

        <mxCell id="weight-cell8" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#A8E6CF;strokeColor=none;opacity=50;" vertex="1" parent="1">
          <mxGeometry x="900" y="210" width="15" height="15" as="geometry" />
        </mxCell>

        <!-- 底部：技术流程箭头 -->
        <mxCell id="flow-arrow1" value="" style="endArrow=block;html=1;strokeColor=#4A90E2;strokeWidth=3;endFill=1;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="360" y="180" as="sourcePoint" />
            <mxPoint x="390" y="180" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="flow-arrow2" value="" style="endArrow=block;html=1;strokeColor=#D4AC0D;strokeWidth=3;endFill=1;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="760" y="180" as="sourcePoint" />
            <mxPoint x="790" y="180" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- 流程标签 -->
        <mxCell id="process1-label" value="信号抽象" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontColor=#4A90E2;" vertex="1" parent="1">
          <mxGeometry x="360" y="195" width="50" height="15" as="geometry" />
        </mxCell>

        <mxCell id="process2-label" value="注意力计算" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontColor=#D4AC0D;" vertex="1" parent="1">
          <mxGeometry x="760" y="195" width="60" height="15" as="geometry" />
        </mxCell>

        <!-- 底部：技术特征说明 -->
        <mxCell id="tech-features-bg" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#F8F9FA;strokeColor=#6C757D;strokeWidth=1;" vertex="1" parent="1">
          <mxGeometry x="50" y="320" width="1100" height="120" as="geometry" />
        </mxCell>

        <mxCell id="tech-features-title" value="核心技术特征" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=16;fontStyle=1;fontColor=#2F5233;" vertex="1" parent="1">
          <mxGeometry x="550" y="330" width="120" height="20" as="geometry" />
        </mxCell>

        <!-- 特征1：自适应K近邻 -->
        <mxCell id="feature1-icon" value="" style="ellipse;whiteSpace=wrap;html=1;fillColor=#9673A6;strokeColor=#9673A6;" vertex="1" parent="1">
          <mxGeometry x="80" y="360" width="20" height="20" as="geometry" />
        </mxCell>

        <mxCell id="feature1-text" value="自适应K近邻拓扑生成" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontColor=#2F5233;" vertex="1" parent="1">
          <mxGeometry x="110" y="355" width="140" height="30" as="geometry" />
        </mxCell>

        <!-- 特征2：多头注意力 -->
        <mxCell id="feature2-icon" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#FF6B6B;strokeColor=#FF6B6B;" vertex="1" parent="1">
          <mxGeometry x="300" y="360" width="20" height="20" as="geometry" />
        </mxCell>

        <mxCell id="feature2-text" value="多头注意力权重分配" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontColor=#2F5233;" vertex="1" parent="1">
          <mxGeometry x="330" y="355" width="140" height="30" as="geometry" />
        </mxCell>

        <!-- 特征3：信号可靠性 -->
        <mxCell id="feature3-icon" value="" style="triangle;whiteSpace=wrap;html=1;fillColor=#82B366;strokeColor=#82B366;" vertex="1" parent="1">
          <mxGeometry x="520" y="360" width="20" height="20" as="geometry" />
        </mxCell>

        <mxCell id="feature3-text" value="信号可靠性自适应调节" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontColor=#2F5233;" vertex="1" parent="1">
          <mxGeometry x="550" y="355" width="140" height="30" as="geometry" />
        </mxCell>

        <!-- 特征4：异构建模 -->
        <mxCell id="feature4-icon" value="" style="rhombus;whiteSpace=wrap;html=1;fillColor=#6C5CE7;strokeColor=#6C5CE7;" vertex="1" parent="1">
          <mxGeometry x="740" y="360" width="20" height="20" as="geometry" />
        </mxCell>

        <mxCell id="feature4-text" value="标签-天线异构关系建模" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontColor=#2F5233;" vertex="1" parent="1">
          <mxGeometry x="770" y="355" width="140" height="30" as="geometry" />
        </mxCell>

        <!-- 数学公式区域 -->
        <mxCell id="formula-bg" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFF8E1;strokeColor=#F57F17;strokeWidth=1;" vertex="1" parent="1">
          <mxGeometry x="80" y="390" width="830" height="40" as="geometry" />
        </mxCell>

        <mxCell id="formula-content" value="α_{ij}^{(r)} = softmax(LeakyReLU(a_r^T · W_r[h_i || h_j || e_{ij}]))     ⟹     h_i^{(l+1)} = σ(∑_{r∈R} ∑_{j∈N_i^{(r)}} α_{ij}^{(r)} W_r^{(l)} h_j^{(l)})" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;fontColor=#E65100;fontFamily=Courier New;" vertex="1" parent="1">
          <mxGeometry x="100" y="400" width="790" height="20" as="geometry" />
        </mxCell>

      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
