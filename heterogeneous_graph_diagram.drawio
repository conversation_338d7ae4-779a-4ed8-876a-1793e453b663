<mxfile host="app.diagrams.net" modified="2024-01-01T00:00:00.000Z" agent="5.0" etag="xxx" version="22.1.16" type="device">
  <diagram name="异构图结构建模" id="heterogeneous-graph-structure">
    <mxGraphModel dx="1400" dy="800" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1000" pageHeight="700" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />

        <!-- 主标题 -->
        <mxCell id="main-title" value="标签-天线异构图结构建模" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=16;fontStyle=1;fontColor=#2C3E50;" vertex="1" parent="1">
          <mxGeometry x="350" y="30" width="300" height="25" as="geometry" />
        </mxCell>

        <!-- 左侧：物理层表示 -->
        <mxCell id="physical-layer-bg" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#F8F9FA;strokeColor=#DEE2E6;strokeWidth=1;" vertex="1" parent="1">
          <mxGeometry x="80" y="80" width="280" height="180" as="geometry" />
        </mxCell>

        <mxCell id="physical-layer-title" value="物理层" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;fontStyle=1;fontColor=#495057;" vertex="1" parent="1">
          <mxGeometry x="200" y="90" width="60" height="20" as="geometry" />
        </mxCell>

        <!-- 天线节点 -->
        <mxCell id="antenna-label" value="天线节点" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontColor=#6C757D;" vertex="1" parent="1">
          <mxGeometry x="100" y="120" width="60" height="15" as="geometry" />
        </mxCell>

        <mxCell id="antenna1" value="A₁" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#E3F2FD;strokeColor=#1976D2;strokeWidth=2;fontSize=11;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="120" y="140" width="30" height="30" as="geometry" />
        </mxCell>

        <mxCell id="antenna2" value="A₂" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#E3F2FD;strokeColor=#1976D2;strokeWidth=2;fontSize=11;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="170" y="140" width="30" height="30" as="geometry" />
        </mxCell>

        <mxCell id="antenna3" value="A₃" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#E3F2FD;strokeColor=#1976D2;strokeWidth=2;fontSize=11;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="220" y="140" width="30" height="30" as="geometry" />
        </mxCell>

        <mxCell id="antenna4" value="A₄" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#E3F2FD;strokeColor=#1976D2;strokeWidth=2;fontSize=11;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="270" y="140" width="30" height="30" as="geometry" />
        </mxCell>

        <!-- 标签节点 -->
        <mxCell id="tag-label" value="标签节点" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontColor=#6C757D;" vertex="1" parent="1">
          <mxGeometry x="100" y="190" width="60" height="15" as="geometry" />
        </mxCell>

        <mxCell id="tag1" value="T₁" style="ellipse;whiteSpace=wrap;html=1;fillColor=#F3E5F5;strokeColor=#7B1FA2;strokeWidth=2;fontSize=11;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="120" y="210" width="30" height="30" as="geometry" />
        </mxCell>

        <mxCell id="tag2" value="T₂" style="ellipse;whiteSpace=wrap;html=1;fillColor=#F3E5F5;strokeColor=#7B1FA2;strokeWidth=2;fontSize=11;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="170" y="210" width="30" height="30" as="geometry" />
        </mxCell>

        <mxCell id="tag3" value="T₃" style="ellipse;whiteSpace=wrap;html=1;fillColor=#F3E5F5;strokeColor=#7B1FA2;strokeWidth=2;fontSize=11;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="220" y="210" width="30" height="30" as="geometry" />
        </mxCell>

        <mxCell id="tag4" value="T₄" style="ellipse;whiteSpace=wrap;html=1;fillColor=#F3E5F5;strokeColor=#7B1FA2;strokeWidth=2;fontSize=11;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="270" y="210" width="30" height="30" as="geometry" />
        </mxCell>

        <mxCell id="tag5" value="T₅" style="ellipse;whiteSpace=wrap;html=1;fillColor=#F3E5F5;strokeColor=#7B1FA2;strokeWidth=2;fontSize=11;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="320" y="210" width="30" height="30" as="geometry" />
        </mxCell>

        <!-- 中间：异构图表示 -->
        <mxCell id="graph-layer-bg" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#F8F9FA;strokeColor=#DEE2E6;strokeWidth=1;" vertex="1" parent="1">
          <mxGeometry x="420" y="80" width="280" height="180" as="geometry" />
        </mxCell>

        <mxCell id="graph-layer-title" value="异构图表示" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;fontStyle=1;fontColor=#495057;" vertex="1" parent="1">
          <mxGeometry x="540" y="90" width="80" height="20" as="geometry" />
        </mxCell>

        <!-- 天线节点层 -->
        <mxCell id="antenna-graph-label" value="天线节点" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontColor=#6C757D;" vertex="1" parent="1">
          <mxGeometry x="440" y="120" width="60" height="15" as="geometry" />
        </mxCell>

        <mxCell id="antenna-g1" value="A₁" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#E3F2FD;strokeColor=#1976D2;strokeWidth=2;fontSize=11;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="460" y="140" width="30" height="30" as="geometry" />
        </mxCell>

        <mxCell id="antenna-g2" value="A₂" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#E3F2FD;strokeColor=#1976D2;strokeWidth=2;fontSize=11;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="510" y="140" width="30" height="30" as="geometry" />
        </mxCell>

        <mxCell id="antenna-g3" value="A₃" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#E3F2FD;strokeColor=#1976D2;strokeWidth=2;fontSize=11;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="560" y="140" width="30" height="30" as="geometry" />
        </mxCell>

        <mxCell id="antenna-g4" value="A₄" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#E3F2FD;strokeColor=#1976D2;strokeWidth=2;fontSize=11;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="610" y="140" width="30" height="30" as="geometry" />
        </mxCell>

        <!-- 标签节点层 -->
        <mxCell id="tag-graph-label" value="标签节点" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontColor=#6C757D;" vertex="1" parent="1">
          <mxGeometry x="440" y="190" width="60" height="15" as="geometry" />
        </mxCell>

        <mxCell id="tag-g1" value="T₁" style="ellipse;whiteSpace=wrap;html=1;fillColor=#F3E5F5;strokeColor=#7B1FA2;strokeWidth=2;fontSize=11;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="460" y="210" width="30" height="30" as="geometry" />
        </mxCell>

        <mxCell id="tag-g2" value="T₂" style="ellipse;whiteSpace=wrap;html=1;fillColor=#F3E5F5;strokeColor=#7B1FA2;strokeWidth=2;fontSize=11;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="510" y="210" width="30" height="30" as="geometry" />
        </mxCell>

        <mxCell id="tag-g3" value="T₃" style="ellipse;whiteSpace=wrap;html=1;fillColor=#F3E5F5;strokeColor=#7B1FA2;strokeWidth=2;fontSize=11;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="560" y="210" width="30" height="30" as="geometry" />
        </mxCell>

        <mxCell id="tag-g4" value="T₄" style="ellipse;whiteSpace=wrap;html=1;fillColor=#F3E5F5;strokeColor=#7B1FA2;strokeWidth=2;fontSize=11;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="610" y="210" width="30" height="30" as="geometry" />
        </mxCell>

        <mxCell id="tag-g5" value="T₅" style="ellipse;whiteSpace=wrap;html=1;fillColor=#F3E5F5;strokeColor=#7B1FA2;strokeWidth=2;fontSize=11;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="660" y="210" width="30" height="30" as="geometry" />
        </mxCell>

        <!-- 异构图边连接 -->
        <!-- T-A 边 (标签-天线) -->
        <mxCell id="ta-edge1" value="" style="endArrow=none;html=1;strokeColor=#FF7043;strokeWidth=2;" edge="1" parent="1" source="tag-g1" target="antenna-g1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="475" y="210" as="sourcePoint" />
            <mxPoint x="475" y="170" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="ta-edge2" value="" style="endArrow=none;html=1;strokeColor=#FF7043;strokeWidth=2;" edge="1" parent="1" source="tag-g2" target="antenna-g2">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="525" y="210" as="sourcePoint" />
            <mxPoint x="525" y="170" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="ta-edge3" value="" style="endArrow=none;html=1;strokeColor=#FF7043;strokeWidth=2;" edge="1" parent="1" source="tag-g3" target="antenna-g3">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="575" y="210" as="sourcePoint" />
            <mxPoint x="575" y="170" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="ta-edge4" value="" style="endArrow=none;html=1;strokeColor=#FF7043;strokeWidth=2;" edge="1" parent="1" source="tag-g4" target="antenna-g4">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="625" y="210" as="sourcePoint" />
            <mxPoint x="625" y="170" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- T-T 边 (标签-标签) -->
        <mxCell id="tt-edge1" value="" style="endArrow=none;html=1;strokeColor=#7B1FA2;strokeWidth=1;dashed=1;" edge="1" parent="1" source="tag-g1" target="tag-g2">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="490" y="225" as="sourcePoint" />
            <mxPoint x="510" y="225" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="tt-edge2" value="" style="endArrow=none;html=1;strokeColor=#7B1FA2;strokeWidth=1;dashed=1;" edge="1" parent="1" source="tag-g2" target="tag-g3">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="540" y="225" as="sourcePoint" />
            <mxPoint x="560" y="225" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="tt-edge3" value="" style="endArrow=none;html=1;strokeColor=#7B1FA2;strokeWidth=1;dashed=1;" edge="1" parent="1" source="tag-g3" target="tag-g4">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="590" y="225" as="sourcePoint" />
            <mxPoint x="610" y="225" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- A-A 边 (天线-天线) -->
        <mxCell id="aa-edge1" value="" style="endArrow=none;html=1;strokeColor=#1976D2;strokeWidth=1;strokeDashArray=3 3;" edge="1" parent="1" source="antenna-g1" target="antenna-g2">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="490" y="155" as="sourcePoint" />
            <mxPoint x="510" y="155" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="aa-edge2" value="" style="endArrow=none;html=1;strokeColor=#1976D2;strokeWidth=1;strokeDashArray=3 3;" edge="1" parent="1" source="antenna-g2" target="antenna-g3">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="540" y="155" as="sourcePoint" />
            <mxPoint x="560" y="155" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="aa-edge3" value="" style="endArrow=none;html=1;strokeColor=#1976D2;strokeWidth=1;strokeDashArray=3 3;" edge="1" parent="1" source="antenna-g3" target="antenna-g4">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="590" y="155" as="sourcePoint" />
            <mxPoint x="610" y="155" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- 右侧：注意力权重可视化 -->
        <mxCell id="attention-layer-bg" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#F8F9FA;strokeColor=#DEE2E6;strokeWidth=1;" vertex="1" parent="1">
          <mxGeometry x="760" y="80" width="200" height="180" as="geometry" />
        </mxCell>

        <mxCell id="attention-layer-title" value="注意力权重" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;fontStyle=1;fontColor=#495057;" vertex="1" parent="1">
          <mxGeometry x="840" y="90" width="80" height="20" as="geometry" />
        </mxCell>

        <!-- 注意力权重矩阵 -->
        <mxCell id="attention-matrix-bg" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#CED4DA;strokeWidth=1;" vertex="1" parent="1">
          <mxGeometry x="780" y="120" width="160" height="120" as="geometry" />
        </mxCell>

        <!-- 权重矩阵标签 -->
        <mxCell id="matrix-row-label" value="T" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontColor=#6C757D;" vertex="1" parent="1">
          <mxGeometry x="760" y="170" width="15" height="20" as="geometry" />
        </mxCell>

        <mxCell id="matrix-col-label" value="A" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontColor=#6C757D;" vertex="1" parent="1">
          <mxGeometry x="850" y="105" width="20" height="15" as="geometry" />
        </mxCell>

        <!-- 权重热力图 - 第一行 -->
        <mxCell id="weight-11" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#FF5722;strokeColor=#FFFFFF;strokeWidth=1;" vertex="1" parent="1">
          <mxGeometry x="800" y="130" width="25" height="25" as="geometry" />
        </mxCell>

        <mxCell id="weight-12" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#FF9800;strokeColor=#FFFFFF;strokeWidth=1;" vertex="1" parent="1">
          <mxGeometry x="830" y="130" width="25" height="25" as="geometry" />
        </mxCell>

        <mxCell id="weight-13" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#FFC107;strokeColor=#FFFFFF;strokeWidth=1;" vertex="1" parent="1">
          <mxGeometry x="860" y="130" width="25" height="25" as="geometry" />
        </mxCell>

        <mxCell id="weight-14" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#FFEB3B;strokeColor=#FFFFFF;strokeWidth=1;" vertex="1" parent="1">
          <mxGeometry x="890" y="130" width="25" height="25" as="geometry" />
        </mxCell>

        <!-- 权重热力图 - 第二行 -->
        <mxCell id="weight-21" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#FF9800;strokeColor=#FFFFFF;strokeWidth=1;" vertex="1" parent="1">
          <mxGeometry x="800" y="160" width="25" height="25" as="geometry" />
        </mxCell>

        <mxCell id="weight-22" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#FF5722;strokeColor=#FFFFFF;strokeWidth=1;" vertex="1" parent="1">
          <mxGeometry x="830" y="160" width="25" height="25" as="geometry" />
        </mxCell>

        <mxCell id="weight-23" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#FFC107;strokeColor=#FFFFFF;strokeWidth=1;" vertex="1" parent="1">
          <mxGeometry x="860" y="160" width="25" height="25" as="geometry" />
        </mxCell>

        <mxCell id="weight-24" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor="#CDDC39" strokeColor="#FFFFFF" strokeWidth="1" vertex="1" parent="1">
          <mxGeometry x="890" y="160" width="25" height="25" as="geometry" />
        </mxCell>

        <!-- 权重热力图 - 第三行 -->
        <mxCell id="weight-31" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#FFC107;strokeColor=#FFFFFF;strokeWidth=1;" vertex="1" parent="1">
          <mxGeometry x="800" y="190" width="25" height="25" as="geometry" />
        </mxCell>

        <mxCell id="weight-32" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#FFEB3B;strokeColor=#FFFFFF;strokeWidth=1;" vertex="1" parent="1">
          <mxGeometry x="830" y="190" width="25" height="25" as="geometry" />
        </mxCell>

        <mxCell id="weight-33" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#FF5722;strokeColor=#FFFFFF;strokeWidth=1;" vertex="1" parent="1">
          <mxGeometry x="860" y="190" width="25" height="25" as="geometry" />
        </mxCell>

        <mxCell id="weight-34" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#FF9800;strokeColor=#FFFFFF;strokeWidth=1;" vertex="1" parent="1">
          <mxGeometry x="890" y="190" width="25" height="25" as="geometry" />
        </mxCell>

        <!-- 流程箭头 -->
        <mxCell id="flow-arrow1" value="" style="endArrow=block;html=1;strokeColor=#6C757D;strokeWidth=2;endFill=1;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="370" y="170" as="sourcePoint" />
            <mxPoint x="410" y="170" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="flow-arrow2" value="" style="endArrow=block;html=1;strokeColor=#6C757D;strokeWidth=2;endFill=1;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="710" y="170" as="sourcePoint" />
            <mxPoint x="750" y="170" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- 流程标签 -->
        <mxCell id="process1-label" value="图建模" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontColor=#6C757D;" vertex="1" parent="1">
          <mxGeometry x="375" y="180" width="40" height="15" as="geometry" />
        </mxCell>

        <mxCell id="process2-label" value="注意力权重" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontColor=#6C757D;" vertex="1" parent="1">
          <mxGeometry x="715" y="180" width="60" height="15" as="geometry" />
        </mxCell>

        <!-- 底部：图例说明 -->
        <mxCell id="legend-bg" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#F8F9FA;strokeColor=#DEE2E6;strokeWidth=1;" vertex="1" parent="1">
          <mxGeometry x="80" y="300" width="880" height="100" as="geometry" />
        </mxCell>

        <mxCell id="legend-title" value="图例说明" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;fontStyle=1;fontColor=#495057;" vertex="1" parent="1">
          <mxGeometry x="500" y="310" width="80" height="20" as="geometry" />
        </mxCell>

        <!-- 节点类型图例 -->
        <mxCell id="legend-antenna-node" value="A" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#E3F2FD;strokeColor=#1976D2;strokeWidth=2;fontSize=10;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="120" y="340" width="20" height="20" as="geometry" />
        </mxCell>

        <mxCell id="legend-antenna-text" value="天线节点" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontColor=#495057;" vertex="1" parent="1">
          <mxGeometry x="150" y="340" width="60" height="20" as="geometry" />
        </mxCell>

        <mxCell id="legend-tag-node" value="T" style="ellipse;whiteSpace=wrap;html=1;fillColor=#F3E5F5;strokeColor=#7B1FA2;strokeWidth=2;fontSize=10;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="240" y="340" width="20" height="20" as="geometry" />
        </mxCell>

        <mxCell id="legend-tag-text" value="标签节点" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontColor=#495057;" vertex="1" parent="1">
          <mxGeometry x="270" y="340" width="60" height="20" as="geometry" />
        </mxCell>

        <!-- 边类型图例 -->
        <mxCell id="legend-ta-edge" value="" style="endArrow=none;html=1;strokeColor=#FF7043;strokeWidth=2;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="380" y="350" as="sourcePoint" />
            <mxPoint x="410" y="350" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="legend-ta-text" value="标签-天线边" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontColor=#495057;" vertex="1" parent="1">
          <mxGeometry x="420" y="340" width="80" height="20" as="geometry" />
        </mxCell>

        <mxCell id="legend-tt-edge" value="" style="endArrow=none;html=1;strokeColor=#7B1FA2;strokeWidth=1;dashed=1;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="530" y="350" as="sourcePoint" />
            <mxPoint x="560" y="350" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="legend-tt-text" value="标签-标签边" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontColor=#495057;" vertex="1" parent="1">
          <mxGeometry x="570" y="340" width="80" height="20" as="geometry" />
        </mxCell>

        <mxCell id="legend-aa-edge" value="" style="endArrow=none;html=1;strokeColor=#1976D2;strokeWidth=1;strokeDashArray=3 3;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="680" y="350" as="sourcePoint" />
            <mxPoint x="710" y="350" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="legend-aa-text" value="天线-天线边" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontColor=#495057;" vertex="1" parent="1">
          <mxGeometry x="720" y="340" width="80" height="20" as="geometry" />
        </mxCell>

        <!-- 权重说明 -->
        <mxCell id="legend-weight-high" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#FF5722;strokeColor=#FFFFFF;strokeWidth=1;" vertex="1" parent="1">
          <mxGeometry x="120" y="370" width="15" height="15" as="geometry" />
        </mxCell>

        <mxCell id="legend-weight-text" value="高权重        中权重        低权重" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontColor=#495057;" vertex="1" parent="1">
          <mxGeometry x="145" y="365" width="200" height="20" as="geometry" />
        </mxCell>

        <mxCell id="legend-weight-med" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#FFC107;strokeColor=#FFFFFF;strokeWidth=1;" vertex="1" parent="1">
          <mxGeometry x="200" y="370" width="15" height="15" as="geometry" />
        </mxCell>

        <mxCell id="legend-weight-low" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#FFEB3B;strokeColor=#FFFFFF;strokeWidth=1;" vertex="1" parent="1">
          <mxGeometry x="280" y="370" width="15" height="15" as="geometry" />
        </mxCell>

      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
