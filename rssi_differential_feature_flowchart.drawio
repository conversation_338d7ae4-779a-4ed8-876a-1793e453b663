<?xml version="1.0" encoding="UTF-8"?>
<mxfile host="app.diagrams.net" agent="5.0" version="24.7.17">
  <diagram name="RSSI差分特征构建流程图" id="rssi-differential-feature-flowchart">
    <mxGraphModel dx="1422" dy="754" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1169" pageHeight="827" math="1" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        
        <!-- Title -->
        <mxCell id="title" value="RSSI差分特征构建流程图 (RSSI Differential Feature Construction Flowchart)" style="text;strokeColor=none;fillColor=none;html=1;fontSize=16;fontStyle=1;verticalAlign=middle;align=center;fontFamily=Times New Roman;" vertex="1" parent="1">
          <mxGeometry x="234.5" y="30" width="700" height="30" as="geometry" />
        </mxCell>
        
        <!-- Step 1: Raw RSSI Input -->
        <mxCell id="step1_box" value="步骤1: 原始RSSI数据采集&lt;br&gt;&lt;b&gt;Raw RSSI Data Collection&lt;/b&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=12;fontFamily=Times New Roman;" vertex="1" parent="1">
          <mxGeometry x="80" y="100" width="160" height="60" as="geometry" />
        </mxCell>
        
        <!-- RSSI Matrix Visualization -->
        <mxCell id="rssi_matrix" value="&lt;b&gt;天线阵列RSSI矩阵&lt;/b&gt;&lt;br&gt;Antenna Array RSSI Matrix&lt;br&gt;&lt;br&gt;$$\mathbf{R} = \begin{bmatrix} r_{11} &amp; r_{12} &amp; r_{13} &amp; r_{14} \\ r_{21} &amp; r_{22} &amp; r_{23} &amp; r_{24} \\ r_{31} &amp; r_{32} &amp; r_{33} &amp; r_{34} \\ r_{41} &amp; r_{42} &amp; r_{43} &amp; r_{44} \end{bmatrix}$$&lt;br&gt;&lt;br&gt;&lt;font color=&quot;#666666&quot;&gt;4×4天线, m=4&lt;/font&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=10;fontFamily=Times New Roman;align=center;" vertex="1" parent="1">
          <mxGeometry x="290" y="80" width="200" height="100" as="geometry" />
        </mxCell>
        
        <!-- Arrow 1 -->
        <mxCell id="arrow1" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#1f497d;" edge="1" parent="1" source="step1_box" target="rssi_matrix">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- Step 2: Normalization -->
        <mxCell id="step2_box" value="步骤2: MinMax标准化&lt;br&gt;&lt;b&gt;MinMax Normalization&lt;/b&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=12;fontFamily=Times New Roman;" vertex="1" parent="1">
          <mxGeometry x="80" y="220" width="160" height="60" as="geometry" />
        </mxCell>
        
        <!-- Normalization Formula -->
        <mxCell id="norm_formula" value="&lt;b&gt;标准化公式&lt;/b&gt;&lt;br&gt;Normalization Formula&lt;br&gt;&lt;br&gt;$$\text{RSSI}_{\text{norm},i}^{(a)} = \frac{\text{RSSI}_i^{(a)} - \min(\mathcal{R}^{(a)})}{\max(\mathcal{R}^{(a)}) - \min(\mathcal{R}^{(a)}) + \epsilon}$$&lt;br&gt;&lt;br&gt;&lt;font color=&quot;#666666&quot;&gt;消除设备间系统性偏差&lt;br&gt;Eliminate systematic bias&lt;/font&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=10;fontFamily=Times New Roman;align=center;" vertex="1" parent="1">
          <mxGeometry x="290" y="200" width="240" height="100" as="geometry" />
        </mxCell>
        
        <!-- Arrow 2 -->
        <mxCell id="arrow2" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#9673a6;" edge="1" parent="1" source="step2_box" target="norm_formula">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- Arrow down from step1 to step2 -->
        <mxCell id="arrow_down1" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#333333;" edge="1" parent="1" source="step1_box" target="step2_box">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- Step 3: Differential Calculation -->
        <mxCell id="step3_box" value="步骤3: 差分特征计算&lt;br&gt;&lt;b&gt;Differential Feature Calculation&lt;/b&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=12;fontFamily=Times New Roman;" vertex="1" parent="1">
          <mxGeometry x="80" y="340" width="160" height="60" as="geometry" />
        </mxCell>
        
        <!-- Differential Formula -->
        <mxCell id="diff_formula" value="&lt;b&gt;差分计算公式&lt;/b&gt;&lt;br&gt;Differential Calculation Formula&lt;br&gt;&lt;br&gt;$$\Delta\text{RSSI}_{i,j}^{(t)} = \text{RSSI}_{\text{norm}}^{(t,a_i)} - \text{RSSI}_{\text{norm}}^{(t,a_j)}$$&lt;br&gt;&lt;br&gt;&lt;font color=&quot;#b85450&quot;&gt;对所有天线对 (i,j), i &amp;lt; j&lt;/font&gt;&lt;br&gt;&lt;font color=&quot;#666666&quot;&gt;消除共模干扰 | Eliminate common-mode interference&lt;/font&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=10;fontFamily=Times New Roman;align=center;" vertex="1" parent="1">
          <mxGeometry x="290" y="320" width="280" height="100" as="geometry" />
        </mxCell>
        
        <!-- Arrow 3 -->
        <mxCell id="arrow3" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#b85450;" edge="1" parent="1" source="step3_box" target="diff_formula">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- Arrow down from step2 to step3 -->
        <mxCell id="arrow_down2" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#333333;" edge="1" parent="1" source="step2_box" target="step3_box">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- Differential Matrix Visualization -->
        <mxCell id="diff_visualization" value="&lt;b&gt;差分特征矩阵构建&lt;/b&gt;&lt;br&gt;Differential Feature Matrix Construction&lt;br&gt;&lt;br&gt;天线对组合: $$C_4^2 = \frac{4 \times 3}{2} = 6$$&lt;br&gt;&lt;br&gt;$$\Delta\mathbf{F} = \begin{bmatrix} r_{12}-r_{11} \\ r_{13}-r_{11} \\ r_{14}-r_{11} \\ r_{23}-r_{22} \\ r_{24}-r_{22} \\ r_{34}-r_{33} \end{bmatrix}$$&lt;br&gt;&lt;br&gt;&lt;font color=&quot;#666666&quot;&gt;16维 → 6维特征压缩&lt;/font&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e6f3ff;strokeColor=#4a90e2;fontSize=10;fontFamily=Times New Roman;align=center;" vertex="1" parent="1">
          <mxGeometry x="620" y="300" width="220" height="140" as="geometry" />
        </mxCell>
        
        <!-- Arrow to differential visualization -->
        <mxCell id="arrow_to_diff_viz" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#4a90e2;" edge="1" parent="1" source="diff_formula" target="diff_visualization">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- Step 4: Nonlinear Transformation -->
        <mxCell id="step4_box" value="步骤4: 非线性特征变换&lt;br&gt;&lt;b&gt;Nonlinear Feature Transformation&lt;/b&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=12;fontFamily=Times New Roman;" vertex="1" parent="1">
          <mxGeometry x="80" y="480" width="160" height="60" as="geometry" />
        </mxCell>
        
        <!-- Nonlinear Formula -->
        <mxCell id="nonlinear_formula" value="&lt;b&gt;非线性映射&lt;/b&gt;&lt;br&gt;Nonlinear Mapping&lt;br&gt;&lt;br&gt;$$f(\mathbf{x}) = \sigma(\mathbf{W} \cdot \mathbf{x} + \mathbf{b})$$&lt;br&gt;&lt;br&gt;其中: $$\mathbf{x} = [\mathbf{x}_{\text{raw}}; \mathbf{x}_{\text{diff}}]$$&lt;br&gt;&lt;br&gt;&lt;font color=&quot;#666666&quot;&gt;σ: ReLU激活函数&lt;br&gt;增强特征表达能力&lt;/font&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=10;fontFamily=Times New Roman;align=center;" vertex="1" parent="1">
          <mxGeometry x="290" y="460" width="260" height="100" as="geometry" />
        </mxCell>
        
        <!-- Arrow 4 -->
        <mxCell id="arrow4" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#82b366;" edge="1" parent="1" source="step4_box" target="nonlinear_formula">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- Arrow down from step3 to step4 -->
        <mxCell id="arrow_down3" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=2;strokeColor=#333333;" edge="1" parent="1" source="step3_box" target="step4_box">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- Final Output -->
        <mxCell id="output_box" value="&lt;b&gt;增强差分特征向量&lt;/b&gt;&lt;br&gt;Enhanced Differential Feature Vector&lt;br&gt;&lt;br&gt;输出: 高维非线性特征表示&lt;br&gt;用于后续图神经网络处理&lt;br&gt;&lt;br&gt;&lt;font color=&quot;#666666&quot;&gt;环境鲁棒性 ↑&lt;br&gt;信号噪声抑制 ↑&lt;br&gt;定位精度 ↑&lt;/font&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6e6;strokeColor=#cc0000;fontSize=11;fontFamily=Times New Roman;fontStyle=1;align=center;" vertex="1" parent="1">
          <mxGeometry x="620" y="460" width="200" height="120" as="geometry" />
        </mxCell>
        
        <!-- Arrow to final output -->
        <mxCell id="arrow_to_output" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=3;strokeColor=#cc0000;" edge="1" parent="1" source="nonlinear_formula" target="output_box">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- Key Benefits Box -->
        <mxCell id="benefits_box" value="&lt;b&gt;关键优势 (Key Benefits):&lt;/b&gt;&lt;br&gt;&lt;br&gt;• 消除发射功率波动影响&lt;br&gt;&lt;font color=&quot;#666666&quot;&gt;Eliminate transmission power fluctuation&lt;/font&gt;&lt;br&gt;&lt;br&gt;• 减少温度变化干扰&lt;br&gt;&lt;font color=&quot;#666666&quot;&gt;Reduce temperature variation interference&lt;/font&gt;&lt;br&gt;&lt;br&gt;• 提高环境适应性&lt;br&gt;&lt;font color=&quot;#666666&quot;&gt;Improve environmental adaptability&lt;/font&gt;&lt;br&gt;&lt;br&gt;• 增强特征判别力&lt;br&gt;&lt;font color=&quot;#666666&quot;&gt;Enhance feature discriminability&lt;/font&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f0f8ff;strokeColor=#1f497d;fontSize=10;fontFamily=Times New Roman;align=left;" vertex="1" parent="1">
          <mxGeometry x="880" y="200" width="250" height="160" as="geometry" />
        </mxCell>
        
        <!-- Technical Specifications Box -->
        <mxCell id="specs_box" value="&lt;b&gt;技术规格 (Technical Specifications):&lt;/b&gt;&lt;br&gt;&lt;br&gt;• 输入维度: m×m 天线矩阵&lt;br&gt;• 输出维度: C_m^2 = m(m-1)/2&lt;br&gt;• 示例: 4×4 → 6 维压缩&lt;br&gt;• 标准化: MinMax [0,1]&lt;br&gt;• 激活函数: ReLU&lt;br&gt;• 计算复杂度: O(m²)&lt;br&gt;&lt;br&gt;&lt;font color=&quot;#666666&quot;&gt;适用于UHF RFID系统&lt;/font&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;fontSize=10;fontFamily=Times New Roman;align=left;" vertex="1" parent="1">
          <mxGeometry x="880" y="400" width="250" height="140" as="geometry" />
        </mxCell>
        
        <!-- Legend -->
        <mxCell id="legend" value="&lt;b&gt;图例 (Legend):&lt;/b&gt;&lt;br&gt;&lt;br&gt;🔵 数据采集 Data Collection&lt;br&gt;🟣 预处理 Preprocessing&lt;br&gt;🔴 特征计算 Feature Calculation&lt;br&gt;🟢 非线性变换 Nonlinear Transform&lt;br&gt;📊 公式说明 Formula Description&lt;br&gt;📈 可视化 Visualization" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#000000;fontSize=10;fontFamily=Times New Roman;align=left;" vertex="1" parent="1">
          <mxGeometry x="40" y="600" width="250" height="120" as="geometry" />
        </mxCell>
        
        <!-- Mathematical Background -->
        <mxCell id="math_background" value="&lt;b&gt;数学原理 (Mathematical Principle):&lt;/b&gt;&lt;br&gt;&lt;br&gt;差分运算具有线性性质:&lt;br&gt;$$\Delta(S + N) = \Delta S + \Delta N$$&lt;br&gt;&lt;br&gt;当噪声N为共模时: $$\Delta N \approx 0$$&lt;br&gt;&lt;br&gt;因此: $$\Delta(S + N) \approx \Delta S$$&lt;br&gt;&lt;br&gt;&lt;font color=&quot;#666666&quot;&gt;有效抑制共模干扰，保留信号特征&lt;/font&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e6ffe6;strokeColor=#00cc00;fontSize=10;fontFamily=Times New Roman;align=center;" vertex="1" parent="1">
          <mxGeometry x="380" y="600" width="300" height="120" as="geometry" />
        </mxCell>
        
        <!-- Algorithm Complexity -->
        <mxCell id="complexity_box" value="&lt;b&gt;算法复杂度分析 (Algorithm Complexity):&lt;/b&gt;&lt;br&gt;&lt;br&gt;• 空间复杂度: O(m²) → O(m²)&lt;br&gt;• 时间复杂度: O(m²)&lt;br&gt;• 特征压缩率: m² → m(m-1)/2&lt;br&gt;• 内存占用: ~50% 减少&lt;br&gt;• 计算效率: 线性时间&lt;br&gt;&lt;br&gt;&lt;font color=&quot;#666666&quot;&gt;适合实时定位应用&lt;/font&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff0f5;strokeColor=#d1477a;fontSize=10;fontFamily=Times New Roman;align=left;" vertex="1" parent="1">
          <mxGeometry x="740" y="600" width="250" height="120" as="geometry" />
        </mxCell>
        
        <!-- Flow indication arrows -->
        <mxCell id="flow_arrow1" value="数据流向" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=4;strokeColor=#ff6b35;dashed=1;" edge="1" parent="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="50" y="130" as="sourcePoint" />
            <mxPoint x="50" y="510" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="flow_label" value="处理流程" style="text;strokeColor=none;fillColor=none;html=1;fontSize=12;fontStyle=1;verticalAlign=middle;align=center;rotation=-90;fontFamily=Times New Roman;color=#ff6b35;" vertex="1" parent="1">
          <mxGeometry x="10" y="310" width="80" height="30" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>