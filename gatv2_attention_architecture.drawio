<mxfile host="app.diagrams.net" modified="2024-01-01T00:00:00.000Z" agent="5.0" etag="xxx" version="22.1.16" type="device">
  <diagram name="GATv2注意力计算架构" id="gatv2-attention-architecture">
    <mxGraphModel dx="1600" dy="900" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1200" pageHeight="800" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />

        <!-- 主标题 -->
        <mxCell id="main-title" value="GATv2注意力计算架构" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=18;fontStyle=1;fontColor=#2C3E50;" vertex="1" parent="1">
          <mxGeometry x="450" y="20" width="300" height="30" as="geometry" />
        </mxCell>

        <!-- 左侧：输入特征层 -->
        <mxCell id="input-layer-bg" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#F8F9FA;strokeColor=#DEE2E6;strokeWidth=1;opacity=30;" vertex="1" parent="1">
          <mxGeometry x="50" y="80" width="200" height="300" as="geometry" />
        </mxCell>

        <!-- 输入图标 -->
        <mxCell id="input-icon" value="📥" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=20;" vertex="1" parent="1">
          <mxGeometry x="60" y="90" width="30" height="20" as="geometry" />
        </mxCell>

        <!-- 标签节点特征 - 用大小表示维度 -->
        <mxCell id="tag-feature-matrix" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#7B1FA2;strokeColor=#4A148C;strokeWidth=2;opacity=90;" vertex="1" parent="1">
          <mxGeometry x="80" y="130" width="80" height="70" as="geometry" />
        </mxCell>

        <!-- 特征矩阵内部结构可视化 -->
        <mxCell id="tag-feature-row1" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=none;opacity=80;" vertex="1" parent="1">
          <mxGeometry x="85" y="135" width="70" height="8" as="geometry" />
        </mxCell>

        <mxCell id="tag-feature-row2" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=none;opacity=60;" vertex="1" parent="1">
          <mxGeometry x="85" y="148" width="70" height="8" as="geometry" />
        </mxCell>

        <mxCell id="tag-feature-row3" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=none;opacity=70;" vertex="1" parent="1">
          <mxGeometry x="85" y="161" width="70" height="8" as="geometry" />
        </mxCell>

        <!-- 天线节点特征 -->
        <mxCell id="antenna-feature-matrix" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#1976D2;strokeColor=#0D47A1;strokeWidth=2;opacity=90;" vertex="1" parent="1">
          <mxGeometry x="80" y="220" width="80" height="70" as="geometry" />
        </mxCell>

        <!-- 天线特征矩阵内部结构 -->
        <mxCell id="antenna-feature-row1" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=none;opacity=80;" vertex="1" parent="1">
          <mxGeometry x="85" y="225" width="70" height="8" as="geometry" />
        </mxCell>

        <mxCell id="antenna-feature-row2" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=none;opacity=60;" vertex="1" parent="1">
          <mxGeometry x="85" y="238" width="70" height="8" as="geometry" />
        </mxCell>

        <mxCell id="antenna-feature-row3" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=none;opacity=70;" vertex="1" parent="1">
          <mxGeometry x="85" y="251" width="70" height="8" as="geometry" />
        </mxCell>

        <!-- 边特征 - 更小的矩阵 -->
        <mxCell id="edge-feature-matrix" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FF7043;strokeColor=#D84315;strokeWidth=2;opacity=80;" vertex="1" parent="1">
          <mxGeometry x="80" y="320" width="80" height="40" as="geometry" />
        </mxCell>

        <!-- 边特征内部结构 -->
        <mxCell id="edge-feature-row1" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=none;opacity=70;" vertex="1" parent="1">
          <mxGeometry x="85" y="325" width="70" height="6" as="geometry" />
        </mxCell>

        <mxCell id="edge-feature-row2" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=none;opacity=50;" vertex="1" parent="1">
          <mxGeometry x="85" y="335" width="70" height="6" as="geometry" />
        </mxCell>

        <!-- 中间：注意力计算层 -->
        <mxCell id="attention-layer-bg" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#F8F9FA;strokeColor=#DEE2E6;strokeWidth=1;opacity=30;" vertex="1" parent="1">
          <mxGeometry x="300" y="80" width="350" height="300" as="geometry" />
        </mxCell>

        <!-- 计算图标 -->
        <mxCell id="compute-icon" value="⚙️" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=20;" vertex="1" parent="1">
          <mxGeometry x="310" y="90" width="30" height="20" as="geometry" />
        </mxCell>

        <!-- 线性变换 - 用矩阵乘法符号 -->
        <mxCell id="linear-transform-bg" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#4CAF50;strokeColor=#2E7D32;strokeWidth=2;opacity=80;" vertex="1" parent="1">
          <mxGeometry x="320" y="120" width="120" height="40" as="geometry" />
        </mxCell>

        <mxCell id="linear-transform-symbol" value="W×" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=16;fontStyle=1;fontColor=#FFFFFF;" vertex="1" parent="1">
          <mxGeometry x="365" y="135" width="30" height="15" as="geometry" />
        </mxCell>

        <!-- 特征拼接 - 用拼接符号 -->
        <mxCell id="concat-bg" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFC107;strokeColor=#F57F17;strokeWidth=2;opacity=80;" vertex="1" parent="1">
          <mxGeometry x="320" y="180" width="120" height="40" as="geometry" />
        </mxCell>

        <mxCell id="concat-symbol" value="||" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=20;fontStyle=1;fontColor=#FFFFFF;" vertex="1" parent="1">
          <mxGeometry x="370" y="195" width="20" height="15" as="geometry" />
        </mxCell>

        <!-- GATv2改进：动态注意力 - 用视觉强调 -->
        <mxCell id="gatv2-improvement-bg" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E91E63;strokeColor=#AD1457;strokeWidth=3;opacity=90;" vertex="1" parent="1">
          <mxGeometry x="460" y="120" width="170" height="100" as="geometry" />
        </mxCell>

        <!-- 创新标识 -->
        <mxCell id="innovation-badge" value="✨" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=24;fontColor=#FFFFFF;" vertex="1" parent="1">
          <mxGeometry x="470" y="130" width="30" height="20" as="geometry" />
        </mxCell>

        <!-- 动态注意力可视化 -->
        <mxCell id="dynamic-attention-bg" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=none;opacity=80;" vertex="1" parent="1">
          <mxGeometry x="480" y="150" width="130" height="25" as="geometry" />
        </mxCell>

        <!-- 动态权重波形 -->
        <mxCell id="dynamic-wave1" value="" style="curved=1;endArrow=none;html=1;strokeColor=#E91E63;strokeWidth=3;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="490" y="162" as="sourcePoint" />
            <mxPoint x="590" y="162" as="targetPoint" />
            <Array as="points">
              <mxPoint x="510" y="155" />
              <mxPoint x="530" y="168" />
              <mxPoint x="550" y="155" />
              <mxPoint x="570" y="168" />
            </Array>
          </mxGeometry>
        </mxCell>

        <!-- LeakyReLU激活可视化 -->
        <mxCell id="leaky-relu-bg" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=none;opacity=80;" vertex="1" parent="1">
          <mxGeometry x="480" y="185" width="130" height="25" as="geometry" />
        </mxCell>

        <!-- 激活函数曲线 -->
        <mxCell id="relu-curve" value="" style="curved=1;endArrow=none;html=1;strokeColor=#E91E63;strokeWidth=2;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="490" y="202" as="sourcePoint" />
            <mxPoint x="590" y="188" as="targetPoint" />
            <Array as="points">
              <mxPoint x="520" y="202" />
              <mxPoint x="540" y="195" />
            </Array>
          </mxGeometry>
        </mxCell>

        <!-- 多头注意力 -->
        <mxCell id="multihead-bg" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E3F2FD;strokeColor=#2196F3;strokeWidth=1;" vertex="1" parent="1">
          <mxGeometry x="320" y="240" width="310" height="80" as="geometry" />
        </mxCell>

        <mxCell id="multihead-title" value="多头注意力并行计算" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;fontStyle=1;fontColor=#2196F3;" vertex="1" parent="1">
          <mxGeometry x="450" y="250" width="120" height="15" as="geometry" />
        </mxCell>

        <!-- 注意力头1 -->
        <mxCell id="head1-bg" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#BBDEFB;strokeColor=#1976D2;strokeWidth=1;" vertex="1" parent="1">
          <mxGeometry x="340" y="270" width="60" height="40" as="geometry" />
        </mxCell>

        <mxCell id="head1-label" value="Head 1" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontColor=#1976D2;" vertex="1" parent="1">
          <mxGeometry x="360" y="285" width="40" height="15" as="geometry" />
        </mxCell>

        <!-- 注意力头2 -->
        <mxCell id="head2-bg" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#BBDEFB;strokeColor=#1976D2;strokeWidth=1;" vertex="1" parent="1">
          <mxGeometry x="420" y="270" width="60" height="40" as="geometry" />
        </mxCell>

        <mxCell id="head2-label" value="Head 2" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontColor=#1976D2;" vertex="1" parent="1">
          <mxGeometry x="440" y="285" width="40" height="15" as="geometry" />
        </mxCell>

        <!-- 注意力头3 -->
        <mxCell id="head3-bg" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#BBDEFB;strokeColor=#1976D2;strokeWidth=1;" vertex="1" parent="1">
          <mxGeometry x="500" y="270" width="60" height="40" as="geometry" />
        </mxCell>

        <mxCell id="head3-label" value="Head 3" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontColor=#1976D2;" vertex="1" parent="1">
          <mxGeometry x="520" y="285" width="40" height="15" as="geometry" />
        </mxCell>

        <!-- 注意力头4 -->
        <mxCell id="head4-bg" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#BBDEFB;strokeColor=#1976D2;strokeWidth=1;" vertex="1" parent="1">
          <mxGeometry x="580" y="270" width="60" height="40" as="geometry" />
        </mxCell>

        <mxCell id="head4-label" value="Head 4" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontColor=#1976D2;" vertex="1" parent="1">
          <mxGeometry x="600" y="285" width="40" height="15" as="geometry" />
        </mxCell>

        <!-- 右侧：输出聚合层 -->
        <mxCell id="output-layer-bg" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#F8F9FA;strokeColor=#DEE2E6;strokeWidth=1;" vertex="1" parent="1">
          <mxGeometry x="700" y="80" width="200" height="300" as="geometry" />
        </mxCell>

        <mxCell id="output-layer-title" value="特征聚合层" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;fontStyle=1;fontColor=#495057;" vertex="1" parent="1">
          <mxGeometry x="775" y="90" width="100" height="20" as="geometry" />
        </mxCell>

        <!-- 注意力权重聚合 -->
        <mxCell id="attention-aggregation-bg" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E8F5E8;strokeColor=#4CAF50;strokeWidth=1;" vertex="1" parent="1">
          <mxGeometry x="720" y="120" width="160" height="50" as="geometry" />
        </mxCell>

        <mxCell id="attention-aggregation-label" value="注意力权重聚合" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontStyle=1;fontColor=#4CAF50;" vertex="1" parent="1">
          <mxGeometry x="770" y="140" width="100" height="15" as="geometry" />
        </mxCell>

        <!-- 特征更新 -->
        <mxCell id="feature-update-bg" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFF3E0;strokeColor=#FF9800;strokeWidth=1;" vertex="1" parent="1">
          <mxGeometry x="720" y="190" width="160" height="50" as="geometry" />
        </mxCell>

        <mxCell id="feature-update-label" value="节点特征更新" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontStyle=1;fontColor=#FF9800;" vertex="1" parent="1">
          <mxGeometry x="770" y="210" width="100" height="15" as="geometry" />
        </mxCell>

        <!-- 输出特征 -->
        <mxCell id="output-features-bg" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E1F5FE;strokeColor=#0277BD;strokeWidth=1;" vertex="1" parent="1">
          <mxGeometry x="720" y="260" width="160" height="100" as="geometry" />
        </mxCell>

        <mxCell id="output-features-label" value="更新后特征" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontStyle=1;fontColor=#0277BD;" vertex="1" parent="1">
          <mxGeometry x="770" y="270" width="80" height="15" as="geometry" />
        </mxCell>

        <mxCell id="output-tag-feature" value="h'_T" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;fontStyle=1;fontColor=#7B1FA2;" vertex="1" parent="1">
          <mxGeometry x="740" y="295" width="30" height="20" as="geometry" />
        </mxCell>

        <mxCell id="output-antenna-feature" value="h'_A" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;fontStyle=1;fontColor=#1976D2;" vertex="1" parent="1">
          <mxGeometry x="830" y="295" width="30" height="20" as="geometry" />
        </mxCell>

        <!-- 连接箭头 -->
        <mxCell id="arrow1" value="" style="endArrow=block;html=1;strokeColor=#6C757D;strokeWidth=2;endFill=1;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="260" y="180" as="sourcePoint" />
            <mxPoint x="290" y="180" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <mxCell id="arrow2" value="" style="endArrow=block;html=1;strokeColor=#6C757D;strokeWidth=2;endFill=1;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="660" y="230" as="sourcePoint" />
            <mxPoint x="690" y="230" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- 底部：技术优势说明 -->
        <mxCell id="advantages-bg" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#F8F9FA;strokeColor=#DEE2E6;strokeWidth=1;" vertex="1" parent="1">
          <mxGeometry x="50" y="420" width="850" height="120" as="geometry" />
        </mxCell>

        <mxCell id="advantages-title" value="GATv2技术优势" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=16;fontStyle=1;fontColor=#2C3E50;" vertex="1" parent="1">
          <mxGeometry x="450" y="430" width="150" height="20" as="geometry" />
        </mxCell>

        <!-- 优势1 -->
        <mxCell id="advantage1-icon" value="1" style="ellipse;whiteSpace=wrap;html=1;fillColor=#E91E63;strokeColor=#E91E63;fontColor=#FFFFFF;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="80" y="460" width="25" height="25" as="geometry" />
        </mxCell>

        <mxCell id="advantage1-text" value="动态注意力权重计算，提升表达能力" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontColor=#495057;" vertex="1" parent="1">
          <mxGeometry x="115" y="455" width="180" height="30" as="geometry" />
        </mxCell>

        <!-- 优势2 -->
        <mxCell id="advantage2-icon" value="2" style="ellipse;whiteSpace=wrap;html=1;fillColor=#2196F3;strokeColor=#2196F3;fontColor=#FFFFFF;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="320" y="460" width="25" height="25" as="geometry" />
        </mxCell>

        <mxCell id="advantage2-text" value="多头并行计算，捕获多维度关系" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontColor=#495057;" vertex="1" parent="1">
          <mxGeometry x="355" y="455" width="180" height="30" as="geometry" />
        </mxCell>

        <!-- 优势3 -->
        <mxCell id="advantage3-icon" value="3" style="ellipse;whiteSpace=wrap;html=1;fillColor=#4CAF50;strokeColor=#4CAF50;fontColor=#FFFFFF;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="560" y="460" width="25" height="25" as="geometry" />
        </mxCell>

        <mxCell id="advantage3-text" value="异构边类型自适应处理机制" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=11;fontColor=#495057;" vertex="1" parent="1">
          <mxGeometry x="595" y="455" width="180" height="30" as="geometry" />
        </mxCell>

        <!-- 核心贡献 -->
        <mxCell id="contribution-bg" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFEBEE;strokeColor=#E91E63;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="80" y="500" width="720" height="30" as="geometry" />
        </mxCell>

        <mxCell id="contribution-text" value="核心贡献：有效缓解RSSI噪声与遮挡对定位精度的影响，提升异构图节点特征学习能力" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;fontStyle=1;fontColor=#E91E63;" vertex="1" parent="1">
          <mxGeometry x="100" y="510" width="680" height="15" as="geometry" />
        </mxCell>

      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
