<mxfile host="app.diagrams.net" modified="2024-01-01T00:00:00.000Z" agent="5.0" etag="xxx" version="22.1.16" type="device">
  <diagram name="异构图结构" id="heterogeneous-graph">
    <mxGraphModel dx="1422" dy="794" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        
        <!-- 标题 -->
        <mxCell id="title" value="标签-天线异构图结构建模" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=16;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="300" y="30" width="200" height="30" as="geometry" />
        </mxCell>
        
        <!-- 标签节点 -->
        <mxCell id="tag1" value="T₁" style="ellipse;whiteSpace=wrap;html=1;fillColor=#E1D5E7;strokeColor=#9673A6;strokeWidth=2;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="150" y="120" width="50" height="50" as="geometry" />
        </mxCell>
        
        <mxCell id="tag2" value="T₂" style="ellipse;whiteSpace=wrap;html=1;fillColor=#E1D5E7;strokeColor=#9673A6;strokeWidth=2;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="250" y="180" width="50" height="50" as="geometry" />
        </mxCell>
        
        <mxCell id="tag3" value="T₃" style="ellipse;whiteSpace=wrap;html=1;fillColor=#E1D5E7;strokeColor=#9673A6;strokeWidth=2;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="350" y="120" width="50" height="50" as="geometry" />
        </mxCell>
        
        <mxCell id="tag4" value="T₄" style="ellipse;whiteSpace=wrap;html=1;fillColor=#E1D5E7;strokeColor=#9673A6;strokeWidth=2;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="450" y="180" width="50" height="50" as="geometry" />
        </mxCell>
        
        <mxCell id="tag5" value="T₅" style="ellipse;whiteSpace=wrap;html=1;fillColor=#E1D5E7;strokeColor=#9673A6;strokeWidth=2;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="550" y="120" width="50" height="50" as="geometry" />
        </mxCell>
        
        <!-- 天线节点 -->
        <mxCell id="antenna1" value="A₁" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#D5E8D4;strokeColor=#82B366;strokeWidth=2;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="200" y="320" width="50" height="50" as="geometry" />
        </mxCell>
        
        <mxCell id="antenna2" value="A₂" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#D5E8D4;strokeColor=#82B366;strokeWidth=2;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="350" y="320" width="50" height="50" as="geometry" />
        </mxCell>
        
        <mxCell id="antenna3" value="A₃" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#D5E8D4;strokeColor=#82B366;strokeWidth=2;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="500" y="320" width="50" height="50" as="geometry" />
        </mxCell>
        
        <!-- T-T 边 (标签-标签) -->
        <mxCell id="tt1" value="" style="endArrow=none;html=1;strokeColor=#9673A6;strokeWidth=2;dashed=1;" edge="1" parent="1" source="tag1" target="tag2">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="200" y="200" as="sourcePoint" />
            <mxPoint x="250" y="150" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="tt2" value="" style="endArrow=none;html=1;strokeColor=#9673A6;strokeWidth=2;dashed=1;" edge="1" parent="1" source="tag2" target="tag3">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="300" y="200" as="sourcePoint" />
            <mxPoint x="350" y="150" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="tt3" value="" style="endArrow=none;html=1;strokeColor=#9673A6;strokeWidth=2;dashed=1;" edge="1" parent="1" source="tag3" target="tag4">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="170" as="sourcePoint" />
            <mxPoint x="450" y="200" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="tt4" value="" style="endArrow=none;html=1;strokeColor=#9673A6;strokeWidth=2;dashed=1;" edge="1" parent="1" source="tag4" target="tag5">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="500" y="180" as="sourcePoint" />
            <mxPoint x="550" y="150" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- T-A 边 (标签-天线) -->
        <mxCell id="ta1" value="" style="endArrow=none;html=1;strokeColor=#D79B00;strokeWidth=2;" edge="1" parent="1" source="tag1" target="antenna1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="175" y="170" as="sourcePoint" />
            <mxPoint x="225" y="320" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="ta2" value="" style="endArrow=none;html=1;strokeColor=#D79B00;strokeWidth=2;" edge="1" parent="1" source="tag2" target="antenna1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="275" y="230" as="sourcePoint" />
            <mxPoint x="225" y="320" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="ta3" value="" style="endArrow=none;html=1;strokeColor=#D79B00;strokeWidth=2;" edge="1" parent="1" source="tag3" target="antenna2">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="375" y="170" as="sourcePoint" />
            <mxPoint x="375" y="320" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="ta4" value="" style="endArrow=none;html=1;strokeColor=#D79B00;strokeWidth=2;" edge="1" parent="1" source="tag4" target="antenna2">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="475" y="230" as="sourcePoint" />
            <mxPoint x="375" y="320" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="ta5" value="" style="endArrow=none;html=1;strokeColor=#D79B00;strokeWidth=2;" edge="1" parent="1" source="tag5" target="antenna3">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="575" y="170" as="sourcePoint" />
            <mxPoint x="525" y="320" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- A-A 边 (天线-天线) -->
        <mxCell id="aa1" value="" style="endArrow=none;html=1;strokeColor=#82B366;strokeWidth=2;strokeDashArray=5 5;" edge="1" parent="1" source="antenna1" target="antenna2">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="250" y="345" as="sourcePoint" />
            <mxPoint x="350" y="345" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="aa2" value="" style="endArrow=none;html=1;strokeColor=#82B366;strokeWidth=2;strokeDashArray=5 5;" edge="1" parent="1" source="antenna2" target="antenna3">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="400" y="345" as="sourcePoint" />
            <mxPoint x="500" y="345" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- 图例 -->
        <mxCell id="legend-bg" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#F8F8F8;strokeColor=#666666;strokeWidth=1;" vertex="1" parent="1">
          <mxGeometry x="50" y="420" width="300" height="180" as="geometry" />
        </mxCell>
        
        <mxCell id="legend-title" value="图例 (Legend)" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="150" y="430" width="100" height="20" as="geometry" />
        </mxCell>
        
        <!-- 节点类型图例 -->
        <mxCell id="legend-tag" value="Tᵢ" style="ellipse;whiteSpace=wrap;html=1;fillColor=#E1D5E7;strokeColor=#9673A6;strokeWidth=2;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="70" y="460" width="30" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="legend-tag-text" value="标签节点 (Tag Node)" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="110" y="465" width="120" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="legend-antenna" value="Aⱼ" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#D5E8D4;strokeColor=#82B366;strokeWidth=2;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="70" y="500" width="30" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="legend-antenna-text" value="天线节点 (Antenna Node)" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="110" y="505" width="120" height="20" as="geometry" />
        </mxCell>
        
        <!-- 边类型图例 -->
        <mxCell id="legend-tt-line" value="" style="endArrow=none;html=1;strokeColor=#9673A6;strokeWidth=2;dashed=1;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="70" y="545" as="sourcePoint" />
            <mxPoint x="100" y="545" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="legend-tt-text" value="T-T: 标签间连接" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="110" y="535" width="100" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="legend-ta-line" value="" style="endArrow=none;html=1;strokeColor=#D79B00;strokeWidth=2;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="220" y="545" as="sourcePoint" />
            <mxPoint x="250" y="545" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="legend-ta-text" value="T-A: 标签-天线连接" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="260" y="535" width="100" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="legend-aa-line" value="" style="endArrow=none;html=1;strokeColor=#82B366;strokeWidth=2;strokeDashArray=5 5;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="70" y="565" as="sourcePoint" />
            <mxPoint x="100" y="565" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="legend-aa-text" value="A-A: 天线间连接" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="110" y="555" width="100" height="20" as="geometry" />
        </mxCell>
        
        <!-- 数学表示 -->
        <mxCell id="math-bg" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFF2CC;strokeColor=#D6B656;strokeWidth=1;" vertex="1" parent="1">
          <mxGeometry x="400" y="420" width="350" height="120" as="geometry" />
        </mxCell>
        
        <mxCell id="math-title" value="异构图数学定义" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="500" y="430" width="150" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="math-content" value="𝒢 = (𝒱, ℰ, 𝒜, ℛ)&lt;br&gt;𝒱 = 𝒱ₜ ∪ 𝒱ₐ&lt;br&gt;ℰ = ℰₜ₋ₜ ∪ ℰₜ₋ₐ ∪ ℰₐ₋ₜ ∪ ℰₐ₋ₐ&lt;br&gt;其中: 𝒱ₜ为标签节点集，𝒱ₐ为天线节点集" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="420" y="460" width="310" height="70" as="geometry" />
        </mxCell>
        
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
